# HLC Syntax Guide for Generator Agent

## Overview

This document specifies the JSON output format that the Generator agent must produce when translating natural language software requirements into Human Language Code (HLC). The output will be written directly to an HLC.json file without further processing.

## File Structure and Object Hierarchy

The Generator must output a JSON array containing HLC objects. The structure follows these fundamental rules:

### File-Level Object Ordering

When operating in **create mode** (rewriting or creating new HLC.json files):

1. The **entry level object** must be the first object in the array
2. The **top level object** must be the second object in the array
3. All other objects (feature, control, directive, literal) follow in logical order

### Object Types and Restrictions

**Entry Level and Top Level Objects:**

- Can only exist at the file level (root of the JSON array)
- Cannot be nested inside other objects
- Cannot be assigned as values to linkers
- Must exist in every complete HLC file

**Feature, Control, Directive, and Literal Objects:**

- Can be linked to other objects through linkers
- Can be nested within linkers of entry level, top level, feature, and control objects
- Form the detailed specification of software functionality

## Standard Object Structure

Every HLC object follows this consistent JSON structure:

```json
{
  "name": "OBJECT-NAME",
  "type": "object_type",
  "prompt": "Instructions for the translator about what this object represents",
  "docs": ["https://documentation-url.com", "https://reference-url.com"],
  "files": ["./**/*.py", "./**/*.js", "./**/*.css"],
  "node": unique_integer_identifier,
  "linkers": {
    "linker_name": linked_object_or_null
  }
}
```

### Object Properties Explained

**name**: A descriptive identifier for the object, typically in UPPERCASE with hyphens
**type**: One of "entry_level", "top_level", "feature", "control", "directive", or "literal"
**prompt**: Clear instructions for the translator explaining what this object should accomplish
**docs**: Array of documentation URLs relevant to this object's functionality
**files**: File patterns where this object's implementation should be located (filled by translator)
**node**: Unique integer identifier used by the editor for targeting modifications
**linkers**: Object containing named linkers that connect this object to others

## Linker System

Linkers create relationships between objects and enforce type consistency within the HLC structure.

### Linker Rules

1. **Type Consistency**: Each linker can only contain objects of the same type

   - A "rules" linker expecting directives cannot contain control or feature objects
   - A "components" linker expecting features cannot contain directive objects

2. **Linker Availability**: Linkers can be used within:

   - Entry level objects
   - Top level objects
   - Feature objects
   - Control objects

3. **Value Assignment**: Linkers contain either:
   - A complete nested object (with all standard properties)
   - `null` if no object is assigned to that linker

### Common Linker Patterns

**rules**: Typically expects directive objects that define governance and constraints
**components**: Usually expects feature objects that represent sub-functionality
**behaviors**: Often expects control objects that define operational logic
**data**: May expect literal objects containing specific values or configurations

## Object Type Specifications

### Entry Level Object

- **Purpose**: Defines overall project structure and organizational framework
- **Occurrence**: Exactly once per HLC file, always first in array
- **Typical Linkers**: "rules" (directives), "structure" (features)

### Top Level Object

- **Purpose**: Specifies the fundamental nature of the software (web app, desktop app, API, etc.)
- **Occurrence**: Exactly once per HLC file, always second in array
- **Typical Linkers**: "features" (feature objects), "rules" (directives), "behaviors" (controls)

### Feature Object

- **Purpose**: Describes specific functionality that users interact with
- **Occurrence**: Multiple instances allowed, nested within linkers
- **Typical Linkers**: "components" (other features), "rules" (directives), "controls" (control objects)

### Control Object

- **Purpose**: Defines behavioral rules, logic flows, and system responses
- **Occurrence**: Multiple instances allowed, nested within linkers
- **Typical Linkers**: "rules" (directives), "data" (literals)

### Directive Object

- **Purpose**: Establishes guidelines, constraints, and policies
- **Occurrence**: Multiple instances allowed, nested within linkers
- **Typical Linkers**: Usually `null` or "parameters" (literals)

### Literal Object

- **Purpose**: Provides concrete data values, strings, numbers, and raw data
- **Occurrence**: Multiple instances allowed, nested within linkers
- **Typical Linkers**: Usually `null` (literals are typically leaf nodes)

## Node Identification System

Each object must have a unique `node` identifier that serves multiple purposes:

1. **Editor Targeting**: The editor uses node IDs to locate objects for write and append operations
2. **Reference Management**: Maintains object relationships across file modifications
3. **Version Control**: Tracks changes to specific objects over time

### Node ID Guidelines

- Use sequential integers starting from 1
- Entry level object typically gets node 1
- Top level object typically gets node 2
- Subsequent objects receive incrementing node IDs
- Node IDs must be unique within each HLC file

## Output Formatting Best Practices

### Prompt Writing

Write clear, actionable prompts that help the translator understand:

- What functionality the object should implement
- How it relates to other objects in the system
- Any specific technical requirements or constraints

### Documentation References

Include relevant documentation URLs that:

- Provide technical specifications for the object's domain
- Offer implementation guidance for the translator
- Reference established patterns or frameworks

### Logical Object Organization

Structure nested objects to reflect:

- Natural software architecture hierarchies
- User workflow dependencies
- Technical implementation relationships

## Example: Complete HLC Structure

```json
[
  {
    "name": "PROJECT-SETUP",
    "type": "entry_level",
    "prompt": "Initialize a task management application with proper project structure and dependencies",
    "docs": ["https://docs.taskapp.com/setup"],
    "files": [],
    "node": 1,
    "linkers": {
      "rules": {
        "name": "PROJECT-STANDARDS",
        "type": "directive",
        "prompt": "Enforce code quality standards and project organization rules",
        "docs": ["https://standards.dev"],
        "files": [],
        "node": 3,
        "linkers": null
      }
    }
  },
  {
    "name": "WEB-APPLICATION",
    "type": "top_level",
    "prompt": "Create a responsive web application for task management",
    "docs": ["https://webapp.guide.com"],
    "files": [],
    "node": 2,
    "linkers": {
      "features": {
        "name": "USER-AUTHENTICATION",
        "type": "feature",
        "prompt": "Implement secure user login and registration system",
        "docs": ["https://auth.examples.com"],
        "files": [],
        "node": 4,
        "linkers": {
          "controls": {
            "name": "LOGIN-VALIDATION",
            "type": "control",
            "prompt": "Validate user credentials and manage session state",
            "docs": ["https://validation.patterns.com"],
            "files": [],
            "node": 5,
            "linkers": null
          }
        }
      }
    }
  }
]
```

## Critical Requirements Summary

1. **Always** start create mode output with entry level object at index 0
2. **Always** place top level object at index 1 in create mode
3. **Never** nest entry level or top level objects within linkers
4. **Maintain** type consistency within each linker
5. **Assign** unique node IDs to every object
6. **Provide** clear, actionable prompts for the translator
7. **Structure** object hierarchies to reflect logical software architecture

This JSON output becomes the complete HLC specification that drives the software generation process.
