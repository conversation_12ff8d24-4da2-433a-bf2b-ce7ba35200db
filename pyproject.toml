[build-system]
requires = ["setuptools>=61.0.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "speech-cli"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "prompt-toolkit>=3.0.51",
    "pyperclip>=1.9.0",
]

[project.scripts]
speech = "speech_cli.cli:run"

[tool.setuptools.packages.find]
where = ["."]
include = ["speech_cli*"]

