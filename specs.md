# Human Language Code (HLC) Language Specification

## 1. Overview

### Language Name & Version

Human Language Code (HLC) Version 1.0

### Purpose & Philosophy

Human Language Code (HLC) is an intermediate representation language designed to bridge the gap between natural language software requirements and high-level programming languages. HLC serves as a structured, human-readable specification format that captures software architecture, features, and behaviors in a way that can be systematically translated into executable code.

The language philosophy centers on three core principles: human readability (specifications should be understandable to non-programmers), structural completeness (all aspects of software design should be expressible), and translation efficiency (the format should facilitate reliable code generation). HLC accomplishes this through an object-oriented approach where software concepts are represented as typed objects connected through explicit relationships called linkers.

## 2. Getting Started

### Hello, World! Example

Here is a minimal HLC specification for a simple console application:

```json
[
  {
    "name": "HELLO-PROJECT",
    "type": "entry_level",
    "prompt": "Create a simple console application project structure",
    "docs": ["https://console-app-guide.com"],
    "files": [],
    "node": 1,
    "linkers": null
  },
  {
    "name": "CONSOLE-APP",
    "type": "top_level",
    "prompt": "Build a console application that displays a greeting message",
    "docs": ["https://console-patterns.com"],
    "files": [],
    "node": 2,
    "linkers": {
      "features": {
        "name": "GREETING-DISPLAY",
        "type": "feature",
        "prompt": "Display 'Hello, World!' message to the console",
        "docs": ["https://output-examples.com"],
        "files": [],
        "node": 3,
        "linkers": {
          "data": {
            "name": "GREETING-TEXT",
            "type": "literal",
            "prompt": "The text message to display",
            "docs": [],
            "files": [],
            "node": 4,
            "linkers": null
          }
        }
      }
    }
  }
]
```

### How to Run

HLC files use the `.hlc.json` extension and contain valid JSON syntax. The HLC specification is processed by translator agents that convert the structured objects into target programming languages. No direct execution of HLC is supported - it serves purely as an intermediate specification format.

## 3. Lexical Conventions

### Character Set

HLC uses UTF-8 encoding and follows JSON lexical conventions. All text content within HLC objects supports the full Unicode character set.

### Line Endings & Whitespace

Standard JSON whitespace rules apply. Whitespace is not syntactically significant except within string literals. Both Unix (LF) and Windows (CRLF) line endings are supported.

### Comments

HLC does not support traditional code comments. Documentation and explanatory text should be placed in the `prompt` and `docs` fields of objects, which serve as structured commentary for translator agents.

### Identifiers

Object names follow these patterns:

- **Object names**: `[A-Z][A-Z0-9-]*` (uppercase letters and hyphens, must start with letter)
- **Linker names**: `[a-z][a-z0-9_]*` (lowercase letters and underscores, must start with letter)
- **Case sensitivity**: Object names are case-sensitive

Examples:

```
VALID-OBJECT-NAME
USER-AUTH-SYSTEM
invalid-name      // Wrong case
123-INVALID       // Cannot start with number
valid_linker_name
user_settings
InvalidLinker     // Wrong case for linker
```

### Literals

HLC treats all data as JSON literals within the structure:

- **Strings**: JSON string format with double quotes and standard escapes
- **Numbers**: JSON number format (integers and floats)
- **Booleans**: `true` and `false`
- **Null**: `null` value for unassigned linkers
- **Arrays**: JSON arrays for `docs` and `files` fields

### Reserved Words

The following values are reserved for the `type` field:

- `entry_level`
- `top_level`
- `feature`
- `control`
- `directive`
- `literal`

## 4. Syntax Grammar

### EBNF Grammar Definition

```ebnf
<hlc_file>        ::= "[" <object_list> "]"
<object_list>     ::= <object> { "," <object> }
<object>          ::= "{" <object_fields> "}"
<object_fields>   ::= <name_field> "," <type_field> "," <prompt_field> ","
                      <docs_field> "," <files_field> "," <node_field> "," <linkers_field>

<name_field>      ::= "\"name\"" ":" <object_name>
<type_field>      ::= "\"type\"" ":" <object_type>
<prompt_field>    ::= "\"prompt\"" ":" <string_literal>
<docs_field>      ::= "\"docs\"" ":" <string_array>
<files_field>     ::= "\"files\"" ":" <string_array>
<node_field>      ::= "\"node\"" ":" <integer>
<linkers_field>   ::= "\"linkers\"" ":" ( <linker_object> | "null" )

<object_name>     ::= "\"" [A-Z][A-Z0-9-]* "\""
<object_type>     ::= "\"entry_level\"" | "\"top_level\"" | "\"feature\"" |
                      "\"control\"" | "\"directive\"" | "\"literal\""
<string_literal>  ::= "\"" <json_string_content> "\""
<string_array>    ::= "[" [ <string_literal> { "," <string_literal> } ] "]"
<integer>         ::= [0-9]+

<linker_object>   ::= "{" <linker_list> "}"
<linker_list>     ::= <linker_entry> { "," <linker_entry> }
<linker_entry>    ::= <linker_name> ":" ( <object> | "null" )
<linker_name>     ::= "\"" [a-z][a-z0-9_]* "\""
```

### Structural Rules

**File Level Structure**: Every HLC file must be a JSON array where:

1. The first object has `type: "entry_level"`
2. The second object has `type: "top_level"`
3. Remaining objects can be any valid type

**Object Hierarchy**: Objects can be nested through linkers, creating tree structures that represent software architecture relationships.

**Node Uniqueness**: Each object must have a unique integer `node` identifier within the file scope.

## 5. Types & Data Structures

### Object Types

HLC defines six core object types that represent different aspects of software specification:

**Entry Level (`entry_level`)**

- **Purpose**: Defines project-wide structure, dependencies, and organizational framework
- **Occurrence**: Exactly one per file, must be first object
- **Typical linkers**: `rules`, `structure`, `dependencies`

**Top Level (`top_level`)**

- **Purpose**: Specifies the fundamental nature and type of software being built
- **Occurrence**: Exactly one per file, must be second object
- **Typical linkers**: `features`, `rules`, `behaviors`, `architecture`

**Feature (`feature`)**

- **Purpose**: Represents user-facing functionality or system capabilities
- **Occurrence**: Multiple instances allowed, nested within linkers
- **Typical linkers**: `components`, `rules`, `controls`, `data`

**Control (`control`)**

- **Purpose**: Defines behavioral logic, business rules, and system responses
- **Occurrence**: Multiple instances allowed, nested within linkers
- **Typical linkers**: `rules`, `data`, `conditions`

**Directive (`directive`)**

- **Purpose**: Establishes constraints, policies, and governance rules
- **Occurrence**: Multiple instances allowed, nested within linkers
- **Typical linkers**: `parameters`, `exceptions` (often null)

**Literal (`literal`)**

- **Purpose**: Provides concrete data values, configuration parameters, and constants
- **Occurrence**: Multiple instances allowed, typically leaf nodes
- **Typical linkers**: Usually null (literals are data containers)

### Type System

HLC uses a **static, nominative type system** where:

- Every object has exactly one type specified in its `type` field
- Linkers enforce type constraints (each linker accepts only one object type)
- Type checking occurs at the structural level during HLC validation
- No type inference - all types must be explicitly declared

## 6. Control Flow

HLC does not implement traditional control flow constructs like loops or conditionals. Instead, control flow is expressed through object relationships and behavioral specifications:

**Conditional Logic**: Represented through `control` objects that specify decision-making rules and branching behaviors.

**Sequential Processing**: Expressed through linker hierarchies where parent objects define processing order for their linked children.

**Error Handling**: Captured in `directive` objects that specify exception handling policies and error recovery procedures.

Example of control flow specification:

```json
{
  "name": "USER-INPUT-VALIDATION",
  "type": "control",
  "prompt": "Validate user input and handle validation failures gracefully",
  "linkers": {
    "rules": {
      "name": "VALIDATION-POLICY",
      "type": "directive",
      "prompt": "Reject invalid input and display helpful error messages"
    }
  }
}
```

## 7. Functions & Modules

### Function Representation

Functions are not first-class constructs in HLC. Instead, functional behavior is captured through the interaction between `feature` and `control` objects:

- **Feature objects** define what functionality should be available
- **Control objects** specify how that functionality should behave
- **Directive objects** establish rules governing the function's operation

### Module System

HLC uses a project-based module approach:

- **Entry level objects** define the overall project scope and module boundaries
- **Feature objects** can represent modules or packages through hierarchical linking
- **File patterns** in the `files` field indicate where translated code should be organized

### Scoping Rules

Scope in HLC is determined by object hierarchy:

- Parent objects define scope boundaries for their linked children
- `directive` objects can establish scope-wide rules that apply to all objects within their hierarchy
- Global scope is defined by entry level and top level objects

## 8. Standard Library Overview

HLC does not include a traditional standard library. Instead, common functionality is expressed through standardized object patterns and documented best practices:

### Common Object Patterns

**I/O Operations**: Represented through `feature` objects with specific naming conventions and control specifications.

**Data Processing**: Captured through `control` objects that define data transformation and manipulation rules.

**User Interface**: Expressed through hierarchical `feature` objects that represent UI components and their interactions.

**System Integration**: Specified through `directive` objects that define external system interaction policies.

### Documentation Integration

The `docs` field in each object serves as HLC's equivalent to standard library documentation, providing references to:

- Implementation patterns and examples
- API specifications for external dependencies
- Best practice guides for common functionality
- Framework and library documentation

## 9. Error Handling

### Validation Errors

HLC validation occurs at the structural level:

**Syntax Errors**: Invalid JSON structure or missing required fields
**Type Errors**: Incorrect object types or invalid linker assignments  
**Hierarchy Errors**: Missing entry/top level objects or invalid nesting
**Reference Errors**: Duplicate node IDs or orphaned object references

### Semantic Error Handling

Error handling logic is specified within HLC objects:

```json
{
  "name": "ERROR-RECOVERY",
  "type": "directive",
  "prompt": "When database connection fails, retry 3 times then switch to offline mode",
  "linkers": {
    "parameters": {
      "name": "RETRY-CONFIG",
      "type": "literal",
      "prompt": "Maximum retry attempts: 3, timeout: 30 seconds"
    }
  }
}
```

## 10. Tooling & Ecosystem

### File Processing

HLC files are processed through a multi-stage pipeline:

1. **Validation**: JSON syntax and HLC structure verification
2. **Analysis**: Object relationship and dependency resolution
3. **Translation**: Conversion to target programming languages
4. **Generation**: Output of executable code and project files

### Development Workflow

```bash
# Validate HLC file structure
hlc validate project.hlc.json

# Translate to target language
hlc translate project.hlc.json --target python --output ./generated

# Analyze object dependencies
hlc analyze project.hlc.json --show-graph
```

## 11. Examples & Tutorials

### Data Processing Example

```json
[
  {
    "name": "DATA-PROCESSOR",
    "type": "entry_level",
    "prompt": "Create a data processing application with file I/O capabilities",
    "node": 1,
    "linkers": null
  },
  {
    "name": "BATCH-PROCESSOR",
    "type": "top_level",
    "prompt": "Build a batch processing system for CSV data transformation",
    "node": 2,
    "linkers": {
      "features": {
        "name": "FILE-READER",
        "type": "feature",
        "prompt": "Read CSV files from specified directory and parse contents",
        "node": 3,
        "linkers": {
          "controls": {
            "name": "PARSING-LOGIC",
            "type": "control",
            "prompt": "Parse CSV with error handling for malformed rows",
            "node": 4,
            "linkers": null
          }
        }
      }
    }
  }
]
```

### Web Application Example

```json
[
  {
    "name": "WEB-PROJECT",
    "type": "entry_level",
    "prompt": "Initialize web application with modern framework structure",
    "node": 1,
    "linkers": {
      "rules": {
        "name": "SECURITY-STANDARDS",
        "type": "directive",
        "prompt": "Implement HTTPS, input validation, and CSRF protection",
        "node": 5,
        "linkers": null
      }
    }
  },
  {
    "name": "WEB-APP",
    "type": "top_level",
    "prompt": "Create responsive web application with user authentication",
    "node": 2,
    "linkers": {
      "features": {
        "name": "USER-DASHBOARD",
        "type": "feature",
        "prompt": "Display personalized dashboard with user data and navigation",
        "node": 3,
        "linkers": {
          "components": {
            "name": "NAVIGATION-MENU",
            "type": "feature",
            "prompt": "Responsive navigation menu with role-based visibility",
            "node": 4,
            "linkers": null
          }
        }
      }
    }
  }
]
```

## 12. Appendix

### Grammar Summary (Compact BNF)

```bnf
hlc_file    := "[" object_list "]"
object_list := object ("," object)*
object      := "{" name "," type "," prompt "," docs "," files "," node "," linkers "}"
linkers     := "{" linker_entry ("," linker_entry)* "}" | "null"
linker_entry := linker_name ":" (object | "null")
```

### Reserved Symbol Table

| Symbol        | Context      | Meaning                      |
| ------------- | ------------ | ---------------------------- |
| `entry_level` | Object type  | Project structure definition |
| `top_level`   | Object type  | Software type specification  |
| `feature`     | Object type  | User functionality           |
| `control`     | Object type  | Behavioral logic             |
| `directive`   | Object type  | Rules and constraints        |
| `literal`     | Object type  | Data values                  |
| `name`        | Object field | Object identifier            |
| `type`        | Object field | Object type specification    |
| `prompt`      | Object field | Translator instructions      |
| `docs`        | Object field | Documentation references     |
| `files`       | Object field | File location patterns       |
| `node`        | Object field | Unique object identifier     |
| `linkers`     | Object field | Object relationships         |

### Version History

- **HLC 1.0** (Current): Initial specification with six object types and linker system
- Future versions may extend object types and add advanced linking capabilities

### Best Practices

1. **Use descriptive object names** that clearly indicate functionality
2. **Write clear prompts** that provide specific implementation guidance
3. **Include relevant documentation** links for complex functionality
4. **Structure object hierarchies** to reflect logical software architecture
5. **Maintain consistent naming** conventions within projects
6. **Validate HLC structure** before processing with translator tools

This specification provides the complete foundation for understanding and working with Human Language Code as an intermediate representation language for software specification and generation.
