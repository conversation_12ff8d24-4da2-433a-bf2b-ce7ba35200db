from typing import Any, Literal

from langchain_core.messages import AnyMessage
from pydantic import BaseModel


def orchestrator_tools_condition(
    state: list[AnyMessage] | dict[str, Any] | BaseModel,
    messages_key: str = "messages",
) -> Literal["tools", "__end__"]:
    """Use in the conditional_edge to route to the ToolNode if the last message."""
    if isinstance(state, list):
        ai_message = state[-1]
    elif (
        isinstance(state, dict)
        and (messages := state.get(messages_key, []))
        or (messages := getattr(state, messages_key, []))
    ):
        ai_message = messages[-1]
    else:
        raise ValueError(f"No messages found in input state to tool_edge: {state}")
    if hasattr(ai_message, "tool_calls") and len(ai_message.tool_calls) > 0:
        return "tools"
    return "__end__"
