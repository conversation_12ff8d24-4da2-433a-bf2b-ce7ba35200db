from langchain_google_genai import ChatGoogleGenerativeAI

from speech_cli.config import config

from .states import InputState, OutputState, OverallState
from .system_messages import promptor_system_message


def promptor(state: InputState) -> OverallState:
    """Creates a good prompt with essential context for the orchestrator node."""
    llm = ChatGoogleGenerativeAI(
        model=config.model,
        api_key=config.api_key,
        temperature=0.2,
        system_instruction=promptor_system_message,
    )
    structured_llm = llm.with_structured_output(InputState)
    response = structured_llm.invoke(state["messages"])

    return response


def orchestrator(state: OverallState) -> OutputState:
    """Guides the editor node to do a good job with enough context."""
    sytem_message = """
        You are Orchestra<PERSON>, an AI agent specialized in translating natural language"""
