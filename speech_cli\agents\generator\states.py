from typing import Annotated, Literal

from langchain_core.messages import AnyMessage
from langgraph.graph import add_messages
from pydantic import BaseModel, Field


class OverallState(BaseModel):
    """The input and output state."""

    messages: Annotated[list[AnyMessage], add_messages] = Field(
        description="The list of messages."
    )
    route: Literal["orchestrator", "__end__"] = Field(
        description="The route to choose between the end and orchestrator nodes."
    )
    response: str = Field(
        description="The text content of the final AI output at the orchestrator node."
    )


class InputState(BaseModel):
    """The input state."""

    messages: Annotated[list[AnyMessage], add_messages] = Field(
        description="The list of messages."
    )
    route: Literal["orchestrator", "__end__"] = Field(
        description="The route to choose between the end and orchestrator nodes."
    )


class OutputState(BaseModel):
    """The output state."""

    messages: Annotated[list[AnyMessage], add_messages] = Field(
        description="The list of messages."
    )
    response: str = Field(
        description="The text content of the final AI output at the orchestrator node."
    )
