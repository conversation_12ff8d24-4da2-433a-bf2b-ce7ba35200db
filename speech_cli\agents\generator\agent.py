from langgraph.graph import END, START
from langgraph.prebuilt import Too<PERSON><PERSON><PERSON>, tools_condition

from speech_cli.agents import core

from .nodes import orchestrator, promptor
from .states import InputState, OutputState, OverallState


class Generator(core.BaseAgent):
    """Generator class for generating hlc code from prompt."""

    tools = []

    nodes = [
        ("orchestrator", orchestrator),
        ("promptor", promptor),
        ("tools", ToolNode(tools)),
    ]

    overall_state = OverallState
    input_schema = InputState
    output_schema = OutputState

    def add_edges(self, builder):
        """Adds edges to builder."""
        builder.add_edge(START, "promptor")
        builder.add_edge(START, "promptor")
        # builder.add_edge("promptor", "orchestrator")
        # builder.add_conditional_edges("orchestrator", tools_condition)
        # builder.add_edge("tools", "orchestrator")

        return builder

    def generate(self, prompt: str):
        return self.graph.invoke(prompt)

    @property
    def entry_point(self):
        return self.generate
