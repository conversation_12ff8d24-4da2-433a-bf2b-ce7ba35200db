from typing import Any

from langchain_google_genai import ChatGoogleGenerativeAI
from langgraph.graph import END, START, MessagesState, StateGraph
from langgraph.prebuilt import tools_condition

from speech_cli.config import config


class BaseAgent:
    """Base agent class for generator and translator agents."""

    def __init__(self):
        self.model = ChatGoogleGenerativeAI(
            model=config.model,
            google_api_key=config.api_key,
        )

    def build(self):
        """Builds a graph representation of the agent."""
        self.builder = StateGraph(self.state)

    def run(self, initial_state: Any) -> Any:
        """Entry point to every agent."""
        self.graph = self.build()
        return self.entry_point(initial_state)
